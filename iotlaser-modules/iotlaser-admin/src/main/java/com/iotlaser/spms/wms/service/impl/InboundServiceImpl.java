package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import com.iotlaser.spms.mes.service.IProductionInboundService;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.domain.Inbound;
import com.iotlaser.spms.wms.domain.InboundItem;
import com.iotlaser.spms.wms.domain.InboundItemBatch;
import com.iotlaser.spms.wms.domain.bo.InboundBo;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.bo.InventoryLogBo;
import com.iotlaser.spms.wms.domain.vo.*;
import com.iotlaser.spms.wms.enums.*;
import com.iotlaser.spms.wms.mapper.InboundItemBatchMapper;
import com.iotlaser.spms.wms.mapper.InboundItemMapper;
import com.iotlaser.spms.wms.mapper.InboundMapper;
import com.iotlaser.spms.wms.service.IInboundService;
import com.iotlaser.spms.wms.service.IInventoryLogService;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.WMS_INBOUND_CODE;
import static org.dromara.common.core.constant.SystemConstants.YES;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 产品仓库入库单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InboundServiceImpl implements IInboundService {

    private final InboundMapper baseMapper;
    private final InboundItemMapper itemMapper;
    private final InboundItemBatchMapper batchMapper;
    private final Gen gen;
    private final IInventoryService inventoryService;
    private final IInventoryLogService inventoryLogService;
    private final IProductService productService;
    private final ILocationService locationService;

    // ERP 服务注入，用于状态回传
    @Autowired
    @Lazy
    private IPurchaseInboundService purchaseInboundService;
    @Autowired
    @Lazy
    private ISaleReturnService saleReturnService;
    @Autowired
    @Lazy
    private IProductionInboundService productionInboundService;

    /**
     * 查询产品仓库入库单
     *
     * @param inboundId 主键
     * @return 产品仓库入库单
     */
    @Override
    public InboundVo queryById(Long inboundId) {
        return baseMapper.selectVoById(inboundId);
    }

    /**
     * 分页查询产品仓库入库单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品仓库入库单分页列表
     */
    @Override
    public TableDataInfo<InboundVo> queryPageList(InboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Inbound> lqw = buildQueryWrapper(bo);
        Page<InboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品仓库入库单列表
     *
     * @param bo 查询条件
     * @return 产品仓库入库单列表
     */
    @Override
    public List<InboundVo> queryList(InboundBo bo) {
        LambdaQueryWrapper<Inbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * TODO 源头全部交由明细完成暂时搁置 根据来源ID和来源类型查询仓库入库单列表
     *
     * @param sourceId   来源ID
     * @param sourceType 来源类型
     * @return 仓库入库单列表
     */
    @Override
    public List<InboundVo> queryBySourceId(Long sourceId, SourceType sourceType) {
        if (sourceId == null || sourceType != null) {
            throw new ServiceException("来源ID和来源类型不能为空");
        }
        LambdaQueryWrapper<Inbound> wrapper = Wrappers.lambdaQuery();
        //wrapper.eq(Inbound::getSourceId, sourceId);
        //wrapper.eq(Inbound::getSourceType, sourceType);
        wrapper.eq(Inbound::getStatus, "1"); // 只查询有效记录
        wrapper.orderByDesc(Inbound::getCreateTime);

        try {
            List<InboundVo> result = baseMapper.selectVoList(wrapper);
            log.info("根据来源查询仓库入库单完成 - 来源ID: {}, 来源类型: {}, 结果数量: {}",
                sourceId, sourceType, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据来源查询仓库入库单失败 - 来源ID: {}, 来源类型: {}, 错误: {}",
                sourceId, sourceType, e.getMessage(), e);
            throw new ServiceException("查询仓库入库单失败：" + e.getMessage());
        }
    }

    private LambdaQueryWrapper<Inbound> buildQueryWrapper(InboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Inbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Inbound::getInboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getInboundCode()), Inbound::getInboundCode, bo.getInboundCode());
        lqw.eq(bo.getInboundStatus() != null, Inbound::getInboundStatus, bo.getInboundStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Inbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginInboundTime") != null && params.get("endInboundTime") != null,
            Inbound::getInboundTime, params.get("beginInboundTime"), params.get("endInboundTime"));
        return lqw;
    }

    /**
     * 新增产品仓库入库单
     *
     * @param bo 产品仓库入库单
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InboundVo insertByBo(InboundBo bo) {
        try {
            // 生成仓库入库单号
            if (StringUtils.isEmpty(bo.getInboundCode())) {
                bo.setInboundCode(gen.code(WMS_INBOUND_CODE));
            }

            // 设置初始状态
            if (bo.getInboundStatus() == null) {
                bo.setInboundStatus(InboundStatus.PENDING_RECEIPT);
            }
            if (bo.getInboundTime() == null) {
                bo.setInboundTime(LocalDateTime.now());
            }


            // 填充冗余字段
            fillRedundantFields(bo);

            // 转换为实体并校验
            Inbound add = MapstructUtils.convert(bo, Inbound.class);
            validEntityBeforeSave(add);

            // 插入主表
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("保存仓库入库单数据失败");
            } else {
                if(add.getSourceType() == null) {
                    add.setSourceType(SourceType.INBOUND);
                    add.setSourceId(add.getInboundId());
                    add.setSourceCode(add.getInboundCode());
                }
                if(add.getDirectSourceType() == null) {
                    add.setDirectSourceId(add.getInboundId());
                    add.setDirectSourceCode(add.getInboundCode());
                    add.setDirectSourceType(DirectSourceType.INVENTORY_INIT);
                }
                baseMapper.updateById(add);
            }

            bo.setInboundId(add.getInboundId());

            log.info("新增产品仓库入库单成功：{}", add.getInboundCode());
            return MapstructUtils.convert(add, InboundVo.class);
        } catch (Exception e) {
            log.error("新增产品仓库入库单失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品仓库入库单失败：" + e.getMessage());
        }
    }

    /**
     * 修改产品仓库入库单
     *
     * @param bo 产品仓库入库单
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InboundVo updateByBo(InboundBo bo) {
        try {
            // 填充冗余字段
            fillRedundantFields(bo);

            // 转换为实体并校验
            Inbound update = MapstructUtils.convert(bo, Inbound.class);
            validEntityBeforeSave(update);

            // 更新主表
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品仓库入库单失败：仓库入库单不存在或数据未变更");
            }

            log.info("修改产品仓库入库单成功：{}", update.getInboundCode());
            return MapstructUtils.convert(update, InboundVo.class);
        } catch (Exception e) {
            log.error("修改产品仓库入库单失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品仓库入库单失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Inbound entity) {
        // 校验仓库入库单编码唯一性
        if (StringUtils.isNotBlank(entity.getInboundCode())) {
            LambdaQueryWrapper<Inbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inbound::getInboundCode, entity.getInboundCode());
            if (entity.getInboundId() != null) {
                wrapper.ne(Inbound::getInboundId, entity.getInboundId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("仓库入库单编码已存在：" + entity.getInboundCode());
            }
        }
    }

    /**
     * 校验并批量删除产品仓库入库单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验仓库入库单是否可以删除
            List<Inbound> inbounds = baseMapper.selectByIds(ids);
            for (Inbound inbound : inbounds) {
                // 检查仓库入库单状态，只有待收货和部分收货状态的仓库入库单才能删除
                if (inbound.getInboundStatus() != InboundStatus.PENDING_RECEIPT && inbound.getInboundStatus() != InboundStatus.PARTIALLY_RECEIVED) {
                    throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】状态为【" +
                        inbound.getInboundStatus() + "】，不允许删除");
                }

                // 检查是否有关联的库存日志
                checkInboundInventoryLogRelation(inbound.getInboundId());

                // 级联删除仓库入库单明细
                int size = itemMapper.delete(new LambdaQueryWrapper<InboundItem>().in(InboundItem::getInboundId, inbound.getInboundId()));
                log.info("级联删除仓库入库单明细，仓库入库单：{}，明细数量：{}", inbound.getInboundCode(), size);
                log.info("删除仓库入库单校验通过：{}", inbound.getInboundCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除仓库入库单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除仓库入库单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除仓库入库单失败：" + e.getMessage());
        }
    }


    /**
     * 查询产品仓库入库单明细表及其关联信息
     *
     * @param itemId 主键
     * @return 产品仓库入库单明细表
     */
    @Override
    public InboundItemVo queryItemById(Long itemId) {
        return MapstructUtils.convert(itemMapper.queryById(itemId), InboundItemVo.class);
    }

    /**
     * 查询产品仓库入库单明细表列表及其关联信息
     *
     * @param directSourceId 上游ID
     * @return 产品仓库入库单明细表列表
     */
    @Override
    public List<InboundItemVo> queryItemByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        InboundItemBo bo = new InboundItemBo();
        bo.setDirectSourceId(directSourceId);
        bo.setDirectSourceType(directSourceType);
        QueryWrapper<InboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return MapstructUtils.convert(itemMapper.queryPageList(null, queryWrapper), InboundItemVo.class);
    }

    /**
     * 查询产品仓库入库单明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 产品仓库入库单明细表列表
     */
    @Override
    public List<InboundItemVo> queryItemList(InboundItemBo bo) {
        QueryWrapper<InboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return MapstructUtils.convert(itemMapper.queryPageList(null, queryWrapper), InboundItemVo.class);
    }

    /**
     * 分页查询产品仓库入库单明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品仓库入库单明细表分页列表
     */
    @Override
    public TableDataInfo<InboundItemVo> queryItemPageList(InboundItemBo bo, PageQuery pageQuery) {
        QueryWrapper<InboundItem> queryWrapper = buildQueryWrapperWith(bo);
        List<InboundItemVo> result = MapstructUtils.convert(itemMapper.queryPageList(pageQuery.build(), queryWrapper), InboundItemVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<InboundItem> buildQueryWrapperWith(InboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<InboundItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getInboundId() != null, "item.inbound_id", bo.getInboundId());
        wrapper.eq(bo.getInventoryId() != null, "item.inventory_id", bo.getInventoryId());
        wrapper.eq(bo.getSourceId() != null, "item.source_id", bo.getSourceId());
        if (bo.getSourceType() != null) {
            wrapper.eq("item.source_type", bo.getSourceType());
        }
        wrapper.eq(bo.getDirectSourceId() != null, "item.direct_source_id", bo.getDirectSourceId());
        if (bo.getDirectSourceType() != null) {
            wrapper.eq("item.direct_source_type", bo.getDirectSourceType());
        }
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        wrapper.eq(bo.getPriceExclusiveTax() != null, "item.price_exclusive_tax", bo.getPriceExclusiveTax());
        wrapper.eq(bo.getAmountExclusiveTax() != null, "item.amount_exclusive_tax", bo.getAmountExclusiveTax());
        wrapper.eq(bo.getAmount() != null, "item.amount", bo.getAmount());
        wrapper.eq(bo.getTaxRate() != null, "item.tax_rate", bo.getTaxRate());
        wrapper.eq(bo.getTaxAmount() != null, "item.tax_amount", bo.getTaxAmount());
        wrapper.eq(bo.getProductionTime() != null, "item.production_time", bo.getProductionTime());
        wrapper.eq(bo.getExpiryTime() != null, "item.expiry_time", bo.getExpiryTime());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * 根据采购订单创建仓库入库单
     *
     * @param orderVo 采购订单
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseOrder(PurchaseOrderVo orderVo) {
        LoginUser loginUser = getLoginUser();
        // 创建仓库入库单
        Inbound add = new Inbound();
        add.setInboundCode(gen.code(WMS_INBOUND_CODE));

        add.setSourceId(orderVo.getSourceId());
        add.setSourceCode(orderVo.getOrderCode());
        add.setSourceType(orderVo.getSourceType());
        add.setDirectSourceId(orderVo.getOrderId());
        add.setDirectSourceCode(orderVo.getOrderCode());
        add.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);

        add.setInboundStatus(InboundStatus.PENDING_RECEIPT);
        add.setInboundTime(LocalDateTime.now());
        if (loginUser != null) {
            add.setOperatorId(loginUser.getUserId());
            add.setOperatorName(loginUser.getNickname());
        }
        add.setSummary("基于采购订单【" + orderVo.getOrderCode() + "】创建");

        // 创建仓库入库单
        int result = baseMapper.insert(add);
        if (result <= 0) {
            throw new ServiceException("创建仓库入库单失败");
        }

        List<InboundItem> inboundItems = new ArrayList<>();
        for (PurchaseOrderItemVo orderItem : orderVo.getItems()) {
            InboundItem inboundItem = new InboundItem();

            inboundItem.setSourceId(orderVo.getSourceId());
            inboundItem.setSourceCode(orderVo.getSourceCode());
            inboundItem.setSourceType(SourceType.getByValue(orderVo.getSourceType().getValue()));

            inboundItem.setDirectSourceId(orderVo.getOrderId());
            inboundItem.setDirectSourceCode(orderVo.getSourceCode());
            inboundItem.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);
            inboundItem.setDirectSourceItemId(orderItem.getItemId());

            inboundItem.setInboundId(add.getInboundId());
            inboundItem.setProductId(orderItem.getProductId());
            inboundItem.setProductCode(orderItem.getProductCode());
            inboundItem.setProductName(orderItem.getProductName());
            inboundItem.setUnitId(orderItem.getUnitId());
            inboundItem.setUnitCode(orderItem.getUnitCode());
            inboundItem.setUnitName(orderItem.getUnitName());
            inboundItem.setQuantity(orderItem.getQuantity());
            inboundItem.setFinishQuantity(BigDecimal.ZERO);
            inboundItem.setPrice(orderItem.getPrice());
            inboundItem.setPriceExclusiveTax(orderItem.getPriceExclusiveTax());
            inboundItem.setAmount(orderItem.getAmount());
            inboundItem.setAmountExclusiveTax(orderItem.getAmountExclusiveTax());
            inboundItem.setTaxRate(orderItem.getTaxRate());
            inboundItem.setTaxAmount(orderItem.getTaxAmount());
            inboundItems.add(inboundItem);
        }
        boolean insertBatch = itemMapper.insertBatch(inboundItems);
        if (!insertBatch) {
            throw new ServiceException("基于采购订单创建仓库入库单失败");
        }
        return true;
    }

    /**
     * 根据采购仓库入库单创建仓库入库单
     *
     * @param inboundVo 采购订单
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseInbound(PurchaseInboundVo inboundVo) {
        LoginUser loginUser = getLoginUser();
        // 创建仓库入库单
        Inbound add = new Inbound();
        add.setInboundCode(gen.code(WMS_INBOUND_CODE));

        add.setSourceId(inboundVo.getSourceId());
        add.setSourceCode(inboundVo.getSourceCode());
        add.setSourceType(inboundVo.getSourceType());
        add.setDirectSourceId(inboundVo.getInboundId());
        add.setDirectSourceCode(inboundVo.getInboundCode());
        add.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);

        add.setInboundStatus(InboundStatus.PENDING_RECEIPT);
        add.setInboundTime(LocalDateTime.now());
        if (loginUser != null) {
            add.setOperatorId(loginUser.getUserId());
            add.setOperatorName(loginUser.getNickname());
        }
        add.setSummary("基于采购仓库入库单【" + inboundVo.getInboundCode() + "】创建");

        // 创建仓库入库单
        int result = baseMapper.insert(add);
        if (result <= 0) {
            throw new ServiceException("创建仓库入库单失败");
        }

        List<InboundItem> inboundItems = new ArrayList<>();
        for (PurchaseInboundItemVo itemVo : inboundVo.getItems()) {
            InboundItem item = new InboundItem();
            item.setInboundId(add.getInboundId());

            item.setSourceId(inboundVo.getSourceId());
            item.setSourceCode(inboundVo.getSourceCode());
            item.setSourceType(inboundVo.getSourceType());

            item.setDirectSourceId(inboundVo.getInboundId());
            item.setDirectSourceCode(inboundVo.getInboundCode());
            item.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);
            item.setDirectSourceItemId(itemVo.getItemId());

            item.setProductId(itemVo.getProductId());
            item.setProductCode(itemVo.getProductCode());
            item.setProductName(itemVo.getProductName());
            item.setUnitId(itemVo.getUnitId());
            item.setUnitCode(itemVo.getUnitCode());
            item.setUnitName(itemVo.getUnitName());

            item.setLocationId(itemVo.getLocationId());
            item.setLocationCode(itemVo.getLocationCode());
            item.setLocationName(itemVo.getLocationName());

            item.setQuantity(itemVo.getQuantity());
            item.setFinishQuantity(itemVo.getFinishQuantity());
            item.setPrice(itemVo.getPrice());
            item.setPriceExclusiveTax(itemVo.getPriceExclusiveTax());
            item.setAmount(itemVo.getAmount());
            item.setAmountExclusiveTax(itemVo.getAmountExclusiveTax());
            item.setTaxRate(itemVo.getTaxRate());
            item.setTaxAmount(itemVo.getTaxAmount());

            item.setExpiryTime(itemVo.getExpiryTime());
            item.setProductionTime(itemVo.getProductionTime());
            inboundItems.add(item);
        }
        boolean insertItem = itemMapper.insertBatch(inboundItems);
        if (!insertItem) {
            throw new ServiceException("基于采购仓库入库单创建仓库入库单失败");
        }
        log.info("基于采购仓库入库单【{}】创建仓库入库单【{}】成功", inboundVo.getInboundCode(), add.getInboundCode());
        return true;
    }

    /**
     * 根据出库单创建仓库入库单
     *
     * @param outboundVo 出库单
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromOutbound(OutboundVo outboundVo) {
        LoginUser loginUser = getLoginUser();
        // 创建仓库入库单
        Inbound add = new Inbound();
        add.setInboundCode(gen.code(WMS_INBOUND_CODE));

        add.setSourceId(outboundVo.getSourceId());
        add.setSourceCode(outboundVo.getOutboundCode());
        add.setSourceType(outboundVo.getSourceType());
        add.setDirectSourceId(outboundVo.getOutboundId());
        add.setDirectSourceCode(outboundVo.getOutboundCode());
        add.setDirectSourceType(DirectSourceType.OUTBOUND);

        add.setInboundStatus(InboundStatus.PENDING_RECEIPT);
        add.setInboundTime(LocalDateTime.now());
        if (loginUser != null) {
            add.setOperatorId(loginUser.getUserId());
            add.setOperatorName(loginUser.getNickname());
        }
        add.setSummary("基于仓库出库单【" + outboundVo.getOutboundCode() + "】创建");

        // 创建仓库入库单
        int result = baseMapper.insert(add);
        if (result <= 0) {
            throw new ServiceException("创建仓库入库单失败");
        }

        List<InboundItem> inboundItems = new ArrayList<>();
        for (OutboundItemVo itemVo : outboundVo.getItems()) {
            InboundItem item = new InboundItem();
            item.setInboundId(add.getInboundId());

            item.setSourceId(itemVo.getSourceId());
            item.setSourceCode(itemVo.getSourceCode());
            item.setSourceType(itemVo.getSourceType());

            item.setDirectSourceId(outboundVo.getOutboundId());
            item.setDirectSourceCode(outboundVo.getOutboundCode());
            item.setDirectSourceType(DirectSourceType.OUTBOUND);
            item.setDirectSourceItemId(itemVo.getItemId());

            item.setProductId(itemVo.getProductId());
            item.setProductCode(itemVo.getProductCode());
            item.setProductName(itemVo.getProductName());
            item.setUnitId(itemVo.getUnitId());
            item.setUnitCode(itemVo.getUnitCode());
            item.setUnitName(itemVo.getUnitName());
            item.setQuantity(itemVo.getQuantity());
            item.setFinishQuantity(BigDecimal.ZERO);
            item.setPriceExclusiveTax(itemVo.getCostPrice());

            if (itemVo.getProduct().getBatchFlag().equals(YES)) {
                List<InboundItemBatch> itemBatches = new ArrayList<>();
                itemVo.getBatches().forEach(batchVo -> {
                    InboundItemBatch batch = new InboundItemBatch();
                    batch.setInboundId(add.getInboundId());
                    batch.setInternalBatchNumber(batchVo.getInternalBatchNumber());
                    batch.setSupplierBatchNumber(batchVo.getSupplierBatchNumber());
                    batch.setSerialNumber(batchVo.getSerialNumber());

                    batch.setProductId(batchVo.getProductId());
                    batch.setProductCode(batchVo.getProductCode());
                    batch.setProductName(batchVo.getProductName());
                    batch.setUnitId(batchVo.getUnitId());
                    batch.setUnitCode(batchVo.getUnitCode());
                    batch.setUnitName(batchVo.getUnitName());
                    batch.setLocationId(batchVo.getLocationId());
                    batch.setLocationCode(batchVo.getLocationCode());
                    batch.setLocationName(batchVo.getLocationName());

                    batch.setQuantity(batchVo.getQuantity());
                    batch.setPriceExclusiveTax(batchVo.getCostPrice());

                    batch.setProductionTime(batchVo.getProductionTime());
                    batch.setExpiryTime(batchVo.getExpiryTime());

                    itemBatches.add(batch);
                });
                item.setBatches(itemBatches);
            }
            inboundItems.add(item);
        }
        boolean insertItem = itemMapper.insertBatch(inboundItems);
        if (!insertItem) {
            throw new ServiceException("基于移库仓库出库单创建仓库入库单失败");
        }

        for (InboundItem item : inboundItems) {
            if (item.getBatches() != null && !item.getBatches().isEmpty()) {
                item.getBatches().forEach(batch -> batch.setItemId(item.getItemId()));
                boolean insertBatch = batchMapper.insertBatch(item.getBatches());
                if (!insertBatch) {
                    throw new ServiceException("基于移库仓库出库单创建仓库入库单失败");
                }
            }
        }
        log.info("基于移库仓库出库单【{}】创建仓库入库单【{}】成功", outboundVo.getOutboundCode(), add.getInboundCode());
        return true;
    }

    /**
     * TODO查询条件有变化后续完善 检查仓库入库单与库存日志的关联关系
     *
     * @param inboundId 仓库入库单ID
     */
    private void checkInboundInventoryLogRelation(Long inboundId) {
        try {
            // 查询库存日志表中源单据为该仓库入库单的记录
            InventoryLogBo queryBo = new InventoryLogBo();
            queryBo.setSourceId(inboundId);
            List<InventoryLogVo> logs = inventoryLogService.queryList(queryBo);

            if (!logs.isEmpty()) {
                throw new ServiceException("该仓库入库单已产生库存变动记录，不能删除");
            }

            log.debug("仓库入库单【{}】库存日志关联检查通过", inboundId);
        } catch (Exception e) {
            if (e instanceof ServiceException) {
                throw e;
            }
            // 如果库存日志服务不可用，记录警告但不阻止删除
            log.warn("检查仓库入库单【{}】库存日志关联时出现异常：{}", inboundId, e.getMessage());
        }
    }

    /**
     * 检查仓库入库单与仓库入库单明细的关联关系
     * 注意：此方法已被级联删除逻辑替代，现在仓库入库单删除时会自动级联删除明细
     *
     * @param inboundId 仓库入库单ID
     */
    private void checkInboundItemRelation(Long inboundId) {
        // 由于已实现级联删除，此检查方法不再需要阻止删除
        // 级联删除会自动处理明细数据的删除
        log.debug("仓库入库单【{}】关联检查：使用级联删除模式，自动处理明细数据", inboundId);
    }

    /**
     * 取消仓库入库单
     *
     * @param inboundId    仓库入库单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInbound(Long inboundId, String cancelReason) {
        try {
            Inbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                throw new ServiceException("仓库入库单不存在");
            }

            // 校验仓库入库单状态
            if (InboundStatus.CANCELLED == inbound.getInboundStatus()) {
                throw new ServiceException("仓库入库单已取消，不能重复操作");
            }

            if (InboundStatus.COMPLETED == inbound.getInboundStatus()) {
                throw new ServiceException("仓库入库单已完成，不能取消");
            }

            // 校验是否可以取消
            validateInboundForCancel(inbound);

            // 更新仓库入库单状态和取消原因
            inbound.setInboundStatus(InboundStatus.CANCELLED);
            if (StringUtils.isNotBlank(cancelReason)) {
                String currentRemark = StringUtils.isNotBlank(inbound.getRemark()) ? inbound.getRemark() : "";
                inbound.setRemark(currentRemark + " [取消原因：" + cancelReason + "，取消时间：" + DateUtils.getTime() + "]");
            }

            int result = baseMapper.updateById(inbound);
            if (result <= 0) {
                throw new ServiceException("取消仓库入库单失败");
            }

            log.info("取消仓库入库单成功：仓库入库单【{}】，原因【{}】", inbound.getInboundCode(), cancelReason);
            return true;
        } catch (Exception e) {
            log.error("取消仓库入库单失败：{}", e.getMessage(), e);
            throw new ServiceException("取消仓库入库单失败：" + e.getMessage());
        }
    }

    /**
     * 完成仓库入库单
     *
     * @param inboundId 仓库入库单ID
     * @return 是否完成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeInbound(Long inboundId) {
        try {
            Inbound inbound = baseMapper.selectById(inboundId);
            if (inbound == null) {
                throw new ServiceException("仓库入库单不存在");
            }

            // 校验仓库入库单状态
            if (inbound.getInboundStatus() != InboundStatus.PENDING_RECEIPT && inbound.getInboundStatus() != InboundStatus.PARTIALLY_RECEIVED) {
                throw new ServiceException("只有待收货和部分收货状态的仓库入库单才能完成");
            }

            // 校验是否可以完成
            inboundValid(inbound, true);

            // TODO: [WMS→ERP状态回传实现] - 优先级: HIGH - 参考文档 docs/design/README_FLOW.md
            // WMS入库完成后，必须实现状态回传机制，确保数据链路完整性：
            //
            // 1. 采购入库回传：
            //    - 判断 directSourceType == PURCHASE_INBOUND
            //    - 调用 purchaseInboundService.updateStatusByWms(directSourceId, COMPLETED)
            //    - 传递实际入库数量、入库时间等关键信息
            //
            // 2. 销售退货入库回传：
            //    - 判断 directSourceType == SALE_RETURN
            //    - 调用 saleReturnService.updateStatusByWms(directSourceId, COMPLETED)
            //    - 更新退货处理状态和入库确认信息
            //
            // 3. 生产入库回传：
            //    - 判断 directSourceType == PRODUCTION_INBOUND
            //    - 调用 productionInboundService.updateStatusByWms(directSourceId, COMPLETED)
            //    - 同步生产完工数量和入库时间
            //
            // 4. 异常处理：
            //    - 回传失败时记录错误日志，但不影响WMS入库完成
            //    - 建立重试机制或手工补偿机制
            //    - 提供数据一致性检查和修复工具

            notifyUpstreamSystemOnCompletion(inbound);

            // 更新库存记录
            updateInventoryRecords(inbound);

            // 更新仓库入库单状态
            inbound.setInboundStatus(InboundStatus.COMPLETED);
            inbound.setInboundTime(LocalDateTime.now());
            String currentRemark = StringUtils.isNotBlank(inbound.getRemark()) ? inbound.getRemark() : "";
            inbound.setRemark(currentRemark + " [完成仓库入库单时间：" + DateUtils.getTime() + "]");

            int result = baseMapper.updateById(inbound);
            if (result <= 0) {
                throw new ServiceException("完成仓库入库单失败");
            }

            log.info("完成仓库入库单成功：仓库入库单【{}】", inbound.getInboundCode());

            // TODO: [WMS入库完成后，回调ERP] - 参考文档 docs/design/README_FLOW.md
            // WMS入库单完成后，需要调用ERP模块的IPurchaseInboundService，通知其更新状态为“已完成”。
            // if (result) {
            //     // 假设 purchaseInboundService 是注入的ERP模块服务
            //     // purchaseInboundService.completeFromWms(inbound.getDirectSourceId());
            // }

            return true;
        } catch (Exception e) {
            log.error("完成仓库入库单失败：{}", e.getMessage(), e);
            throw new ServiceException("完成仓库入库单失败：" + e.getMessage());
        }
    }

    /**
     * 校验仓库入库单是否可以确认
     *
     * @param inbound 仓库入库单
     */
    private void inboundValid(Inbound inbound, boolean isComplete) {
        InboundItemBo query = new InboundItemBo();
        query.setInboundId(inbound.getInboundId());
        List<InboundItemVo> itemVos = queryItemList(query);
        if (itemVos == null || itemVos.isEmpty()) {
            throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】缺少仓库入库单明细");
        }
        for (InboundItemVo itemVo : itemVos) {
            if (itemVo.getQuantity() == null || itemVo.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细通知仓库入库单数量不能小于等于0");
            }
            if (isComplete) {
                if (itemVo.getFinishQuantity() == null || itemVo.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细通知入库数量不能小于等于0");
                }
                if (itemVo.getQuantity().compareTo(itemVo.getFinishQuantity()) != 0) {
                    throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细通知入库数量不能大于或小于已上架数量数量");
                }
            }
            if (isComplete) {
                if (itemVo.getProduct().getBatchFlag().equals(YES)) {
                    if (itemVo.getBatches().isEmpty()) {
                        throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细缺少批次信息");
                    }
                    BigDecimal totalQuantity = BigDecimal.ZERO;
                    for (InboundItemBatchVo batchVo : itemVo.getBatches()) {
                        if (batchVo.getQuantity() == null || batchVo.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                            throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细【" + itemVo.getProductName() + "】批次【" + batchVo.getInternalBatchNumber() + "】数量不能小于等于0");
                        }
                        if (batchVo.getLocationId() == null) {
                            throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细【" + itemVo.getProductName() + "】批次【" + batchVo.getInternalBatchNumber() + "】未选择仓库入库位置");
                        }
                        totalQuantity = totalQuantity.add(batchVo.getQuantity());
                    }
                    if (!totalQuantity.equals(itemVo.getFinishQuantity())) {
                        throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细【" + itemVo.getProductName() + "】批次数量总和与通知入库数量不一致");
                    }
                } else {
                    if (itemVo.getLocationId() == null) {
                        throw new ServiceException("仓库入库单【" + inbound.getInboundCode() + "】的仓库入库单明细【" + itemVo.getProductName() + "】未选择仓库入库位置");
                    }
                }
            }
        }
        log.debug("仓库入库单【{}】确认校验通过", inbound.getInboundCode());
    }

    /**
     * 校验仓库入库单是否可以取消
     *
     * @param inbound 仓库入库单
     */
    private void validateInboundForCancel(Inbound inbound) {
        // 检查是否已有库存变动
        // TODO: 检查是否有关联的库存日志
        // 如果已有库存变动，需要特殊处理

        log.debug("仓库入库单【{}】取消校验通过", inbound.getInboundCode());
    }

    /**
     * 更新库存记录
     *
     * @param inbound 仓库入库单
     */
    private void updateInventoryRecords(Inbound inbound) {
        try {
            // 基于仓库入库单明细和批次进行库存操作（遵循仓库入库单→明细→批次的标准结构）
            // 实现完整的仓库入库单库存处理流程
            // 获取仓库入库单明细

            InboundItemBo query = new InboundItemBo();
            query.setInboundId(inbound.getInboundId());
            List<InboundItemVo> itemVos = queryItemList(query);
            for (InboundItemVo itemVo : itemVos) {
                // 是否批次管理
                if (itemVo.getProduct().getBatchFlag().equals(YES)) {
                    // 基于仓库入库单批次创建库存记录（批次）
                    processInboundItemBatches(inbound, itemVo);
                } else {
                    // 基于仓库入库单明细创建库存记录（明细）
                    createInventoryFromItem(inbound, itemVo);
                }
            }
            log.info("仓库入库单【{}】库存记录更新完成，处理明细数量：{}", inbound.getInboundCode(), itemVos.size());
        } catch (Exception e) {
            log.error("仓库入库单【{}】库存记录更新失败：{}", inbound.getInboundCode(), e.getMessage(), e);
            throw new ServiceException("库存记录更新失败：" + e.getMessage());
        }
    }

    /**
     * 基于仓库入库单明细创建库存记录（遵循明细→批次的标准结构）
     */
    private void createInventoryFromItem(Inbound inbound, InboundItemVo itemVo) {
        try {
            InventoryBo query = new InventoryBo();
            query.setProductId(itemVo.getProductId());
            query.setInventoryStatus(InventoryStatus.AVAILABLE);

            List<InventoryVo> inventoryVos = inventoryService.queryList(query);

            InventoryBo inventoryBo = new InventoryBo();
            inventoryBo.setManagementType(InventoryManagementType.NONE);

            if (inbound.getSourceType() != null) {
                inventoryBo.setSourceId(inbound.getSourceId());
                inventoryBo.setSourceCode(inbound.getSourceCode());
                inventoryBo.setSourceType(inbound.getSourceType());
            } else {
                inventoryBo.setSourceId(itemVo.getSourceId());
                inventoryBo.setSourceCode(itemVo.getSourceCode());
                inventoryBo.setSourceType(itemVo.getSourceType());
            }

            if (inbound.getDirectSourceId() != null) {
                inventoryBo.setDirectSourceId(inbound.getDirectSourceId());
                inventoryBo.setDirectSourceCode(inbound.getDirectSourceCode());
                inventoryBo.setDirectSourceType(inbound.getDirectSourceType());
            } else {
                inventoryBo.setDirectSourceId(itemVo.getDirectSourceId());
                inventoryBo.setDirectSourceCode(itemVo.getDirectSourceCode());
                inventoryBo.setDirectSourceType(itemVo.getDirectSourceType());
            }
            inventoryBo.setDirectSourceItemId(itemVo.getItemId());
            inventoryBo.setDirectSourceBatchId(0L);

            inventoryBo.setProductId(itemVo.getProductId());
            inventoryBo.setProductCode(itemVo.getProductCode());
            inventoryBo.setProductName(itemVo.getProductName());
            inventoryBo.setUnitId(itemVo.getUnitId());
            inventoryBo.setUnitCode(itemVo.getUnitCode());
            inventoryBo.setUnitName(itemVo.getUnitName());
            inventoryBo.setLocationId(itemVo.getLocationId());
            inventoryBo.setLocationCode(itemVo.getLocationCode());
            inventoryBo.setLocationName(itemVo.getLocationName());

            inventoryBo.setQuantity(itemVo.getQuantity());
            inventoryBo.setCostPrice(itemVo.getPriceExclusiveTax());

            inventoryBo.setExpiryTime(itemVo.getExpiryTime());
            inventoryBo.setInventoryTime(inbound.getInboundTime());

            inventoryBo.setInventoryStatus(InventoryStatus.AVAILABLE); // 使用枚举类型
            inventoryBo.setRemark("仓库入库单：" + inbound.getInboundCode());

            if (inventoryVos != null && !inventoryVos.isEmpty()) {
                inventoryBo.setInventoryId(inventoryVos.getFirst().getInventoryId());
                inventoryBo.setQuantity(itemVo.getFinishQuantity());
                Boolean result = inventoryService.updateByBo(inventoryBo);
                if (!result) {
                    throw new ServiceException("更新库存失败");
                }
                log.info("更新库存成功 - 产品编码: {}, 产品名称: {}, 数量: {}", itemVo.getProductCode(), itemVo.getProductName(), inventoryBo.getQuantity());
            } else {
                Boolean result = inventoryService.insertByBo(inventoryBo);
                if (!result) {
                    throw new ServiceException("创建库存失败");
                }
                log.info("创建库存成功 - 产品编码: {}, 产品名称: {}, 数量: {}", itemVo.getProductCode(), itemVo.getProductName(), inventoryBo.getQuantity());
            }
        } catch (Exception e) {
            log.error("基于明细 - 库存失败 - 产品编码: {}, 产品名称: {}, 错误: {}", itemVo.getProductCode(), itemVo.getProductName(), e.getMessage(), e);
            throw new ServiceException("基于明细 - 库存失败：" + e.getMessage());
        }
    }

    /**
     * TODO需完善 处理仓库入库单明细的批次分配（遵循明细→批次的标准结构）
     */
    private void processInboundItemBatches(Inbound inbound, InboundItemVo item) {
        try {
            // 获取该明细的批次信息
            if (item.getBatches().isEmpty()) {
                log.debug("仓库入库单明细【{}】没有预定义批次，使用默认批次处理", item.getItemId());
                return;
            }

            // 处理每个批次的库存操作
            for (InboundItemBatchVo batch : item.getBatches()) {
                // 基于批次信息创建库存
                createInventoryFromItemBatch(inbound, item, batch);
                log.info("处理仓库入库单明细批次 - 明细ID: {}, 批次: {}, 数量: {}", item.getItemId(), batch.getInternalBatchNumber(), batch.getQuantity());
            }
        } catch (Exception e) {
            log.error("处理仓库入库单明细批次失败 - 明细ID: {}, 错误: {}", item.getItemId(), e.getMessage(), e);
            throw new ServiceException("处理仓库入库单明细批次失败：" + e.getMessage());
        }
    }

    /**
     * 基于仓库入库单明细批次创建库存
     */
    private void createInventoryFromItemBatch(Inbound inbound, InboundItemVo itemVo, InboundItemBatchVo batchVo) {
        try {
            InventoryBo inventoryBo = new InventoryBo();
            inventoryBo.setManagementType(InventoryManagementType.BATCH);
            inventoryBo.setInternalBatchNumber(batchVo.getInternalBatchNumber());
            inventoryBo.setSupplierBatchNumber(batchVo.getSupplierBatchNumber());
            inventoryBo.setSerialNumber(batchVo.getSerialNumber());

            inventoryBo.setSourceId(itemVo.getSourceId());
            inventoryBo.setSourceCode(itemVo.getSourceCode());
            inventoryBo.setSourceType(itemVo.getSourceType());

            inventoryBo.setDirectSourceId(itemVo.getDirectSourceId());
            inventoryBo.setDirectSourceCode(itemVo.getDirectSourceCode());
            inventoryBo.setDirectSourceType(itemVo.getDirectSourceType());
            inventoryBo.setDirectSourceItemId(itemVo.getItemId());
            inventoryBo.setDirectSourceBatchId(batchVo.getBatchId());

            inventoryBo.setProductId(itemVo.getProductId());
            inventoryBo.setProductCode(itemVo.getProductCode());
            inventoryBo.setProductName(itemVo.getProductName());
            inventoryBo.setUnitId(itemVo.getUnitId());
            inventoryBo.setUnitCode(itemVo.getUnitCode());
            inventoryBo.setUnitName(itemVo.getUnitName());
            inventoryBo.setLocationId(batchVo.getLocationId());
            inventoryBo.setLocationCode(batchVo.getLocationCode());
            inventoryBo.setLocationName(batchVo.getLocationName());

            inventoryBo.setQuantity(batchVo.getQuantity());
            inventoryBo.setCostPrice(batchVo.getPriceExclusiveTax());

            inventoryBo.setExpiryTime(batchVo.getExpiryTime());
            inventoryBo.setInventoryTime(inbound.getInboundTime());

            inventoryBo.setInventoryStatus(InventoryStatus.AVAILABLE);

            inventoryBo.setRemark("仓库入库单明细批次：" + inbound.getInboundCode() + "-" + batchVo.getInternalBatchNumber());

            Boolean result = inventoryService.insertByBo(inventoryBo);
            if (!result) {
                throw new ServiceException("创建库存失败");
            }
            log.info("创建库存成功 - 产品编码: {}, 产品名称: {}, 数量: {}", itemVo.getProductCode(), itemVo.getProductName(), inventoryBo.getQuantity());
        } catch (Exception e) {
            log.error("基于批次 - 库存失败 - 明细ID: {}, 批次: {}, 错误: {}", itemVo.getItemId(), batchVo.getInternalBatchNumber(), e.getMessage(), e);
            throw new ServiceException("基于批次 - 库存失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(InboundBo bo) {
        // 填充来源信息（如果有来源ID但缺少来源信息）
        //if (bo.getSourceId() != null && StringUtils.isEmpty(bo.getSourceCode())) {
        // TODO: 根据来源类型填充来源信息
        // 这里需要根据具体的业务逻辑来实现
        //}

        //填充责任人信息
        //LoginUser loginUser = LoginHelper.getLoginUser();
    }

    // TODO: [WMS→ERP状态回传方法实现] - 优先级: HIGH - 参考文档 docs/design/README_FLOW.md
    // 需要实现以下状态回传方法：

    /**
     * 通知上游系统入库完成
     * @param inbound 入库单
     */
    private void notifyUpstreamSystemOnCompletion(Inbound inbound) {
        try {
            DirectSourceType sourceType = inbound.getDirectSourceType();
            Long sourceId = inbound.getDirectSourceId();
            String wmsInboundCode = inbound.getInboundCode();

            // 收集实际入库数量信息
            Map<Long, BigDecimal> actualQuantities = collectActualQuantities(inbound);

            switch (sourceType) {
                case PURCHASE_INBOUND:
                    // 通知采购入库单完成
                    Boolean purchaseResult = purchaseInboundService.updateStatusByWms(
                        sourceId, wmsInboundCode, actualQuantities);
                    if (purchaseResult) {
                        log.info("WMS入库完成，成功通知采购入库单【{}】状态更新", sourceId);
                    } else {
                        log.error("WMS入库完成，通知采购入库单【{}】状态更新失败", sourceId);
                    }
                    break;

                case SALE_RETURN:
                    // 通知销售退货单完成
                    try {
                        // TODO: 需要注入 ISaleReturnService 并实现 updateStatusByWms 方法
                         Boolean returnResult = saleReturnService.updateStatusByWms(sourceId, wmsInboundCode, actualQuantities);
                         if (returnResult) {
                             log.info("WMS入库完成，成功通知销售退货单【{}】状态更新", sourceId);
                         } else {
                             log.error("WMS入库完成，通知销售退货单【{}】状态更新失败", sourceId);
                         }
                        log.info("WMS入库完成，通知销售退货单【{}】状态更新（待实现）", sourceId);
                    } catch (Exception e) {
                        log.error("WMS入库完成，通知销售退货单【{}】状态更新异常: {}", sourceId, e.getMessage(), e);
                    }
                    break;

                case PRODUCTION_INBOUND:
                    // 通知生产入库单完成
                    try {
                        // TODO: 需要注入 IProductionInboundService 并实现 updateStatusByWms 方法
                         Boolean productionResult = productionInboundService.updateStatusByWms(sourceId, wmsInboundCode, actualQuantities);
                         if (productionResult) {
                             log.info("WMS入库完成，成功通知生产入库单【{}】状态更新", sourceId);
                         } else {
                             log.error("WMS入库完成，通知生产入库单【{}】状态更新失败", sourceId);
                         }
                        log.info("WMS入库完成，通知生产入库单【{}】状态更新（待实现）", sourceId);
                    } catch (Exception e) {
                        log.error("WMS入库完成，通知生产入库单【{}】状态更新异常: {}", sourceId, e.getMessage(), e);
                    }
                    break;

                default:
                    log.debug("WMS入库完成，无需通知上游系统，源类型：{}", sourceType);
                    break;
            }
        } catch (Exception e) {
            log.error("WMS入库完成后通知上游系统失败，入库单：{}，错误：{}",
                inbound.getInboundCode(), e.getMessage(), e);

            // 记录异常但不影响WMS入库完成流程
            try {
                notifyUpstreamSystemException(inbound, e.getMessage());
            } catch (Exception ex) {
                log.error("通知上游系统异常失败：{}", ex.getMessage(), ex);
            }
        }
    }

    /**
     * 收集实际入库数量信息
     * @param inbound 入库单
     * @return 实际数量映射 (产品ID -> 实际数量)
     */
    private Map<Long, BigDecimal> collectActualQuantities(Inbound inbound) {
        Map<Long, BigDecimal> actualQuantities = new HashMap<>();

        // TODO: 从入库明细中收集实际入库数量
         List<InboundItem> items = inboundItemService.selectListByInboundId(inbound.getInboundId());
         for (InboundItem item : items) {
             actualQuantities.put(item.getProductId(), item.getActualQuantity());
         }

        log.debug("收集入库单【{}】实际数量信息：{}", inbound.getInboundCode(), actualQuantities);
        return actualQuantities;
    }

    /**
     * 通知上游系统异常
     * @param inbound 入库单
     * @param exceptionMessage 异常信息
     */
    private void notifyUpstreamSystemException(Inbound inbound, String exceptionMessage) {
        try {
            DirectSourceType sourceType = inbound.getDirectSourceType();
            Long sourceId = inbound.getDirectSourceId();

            switch (sourceType) {
                case PURCHASE_INBOUND:
                    purchaseInboundService.handleWmsException(sourceId, exceptionMessage);
                    break;
                case SALE_RETURN:
                     saleReturnService.handleWmsException(sourceId, exceptionMessage);
                    break;
                case PRODUCTION_INBOUND:
                     productionInboundService.handleWmsException(sourceId, exceptionMessage);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("通知上游系统异常失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 数据一致性检查
     * <p>
     * 检查 WMS 和 ERP 之间的数据一致性，发现并记录不一致的情况
     *
     * @param inboundId WMS入库单ID
     * @return 检查结果报告
     */
    public String checkDataConsistency(Long inboundId) {
        StringBuilder report = new StringBuilder();
        report.append("=== WMS-ERP 数据一致性检查报告 ===\n");

        try {
            Inbound wmsInbound = baseMapper.selectById(inboundId);
            if (wmsInbound == null) {
                report.append("❌ WMS入库单不存在: ").append(inboundId).append("\n");
                return report.toString();
            }

            report.append("🔍 检查入库单: ").append(wmsInbound.getInboundCode()).append("\n");

            // 检查状态一致性
            if (wmsInbound.getDirectSourceType() == DirectSourceType.PURCHASE_INBOUND
                && wmsInbound.getDirectSourceId() != null) {

                try {
                    // 查询ERP采购入库单状态
                    PurchaseInboundVo erpInbound = purchaseInboundService.queryById(wmsInbound.getDirectSourceId());
                    if (erpInbound != null) {
                        report.append("✅ ERP采购入库单存在: ").append(erpInbound.getInboundCode()).append("\n");

                        // 检查状态一致性
                        boolean statusConsistent = checkStatusConsistency(wmsInbound.getInboundStatus(), erpInbound.getInboundStatus());
                        if (statusConsistent) {
                            report.append("✅ 状态一致: WMS=").append(wmsInbound.getInboundStatus())
                                  .append(", ERP=").append(erpInbound.getInboundStatus()).append("\n");
                        } else {
                            report.append("❌ 状态不一致: WMS=").append(wmsInbound.getInboundStatus())
                                  .append(", ERP=").append(erpInbound.getInboundStatus()).append("\n");
                        }

                        // 检查数量一致性
                        checkQuantityConsistency(wmsInbound, erpInbound, report);

                    } else {
                        report.append("❌ ERP采购入库单不存在: ").append(wmsInbound.getDirectSourceId()).append("\n");
                    }
                } catch (Exception e) {
                    report.append("❌ 查询ERP采购入库单异常: ").append(e.getMessage()).append("\n");
                }
            }

            report.append("=== 检查完成 ===\n");

        } catch (Exception e) {
            report.append("❌ 数据一致性检查异常: ").append(e.getMessage()).append("\n");
            log.error("数据一致性检查异常 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
        }

        return report.toString();
    }

    /**
     * 检查状态一致性
     */
    private boolean checkStatusConsistency(InboundStatus wmsStatus, PurchaseInboundStatus erpStatus) {
        // 定义状态映射关系
        if (wmsStatus == InboundStatus.COMPLETED && erpStatus == PurchaseInboundStatus.COMPLETED) {
            return true;
        }
        if (wmsStatus == InboundStatus.PARTIALLY_RECEIVED && erpStatus == PurchaseInboundStatus.PENDING_WAREHOUSE) {
            return true;
        }
        // 可以根据业务需要添加更多状态映射
        return false;
    }

    /**
     * 检查数量一致性
     */
    private void checkQuantityConsistency(Inbound wmsInbound, PurchaseInboundVo erpInbound, StringBuilder report) {
        try {
            // TODO: 实现明细数量一致性检查
            // 1. 查询WMS入库明细
            // 2. 查询ERP入库明细
            // 3. 比较产品、数量、金额等关键字段
            // 4. 记录不一致的明细

            report.append("ℹ️ 数量一致性检查（待完善实现）\n");

        } catch (Exception e) {
            report.append("❌ 数量一致性检查异常: ").append(e.getMessage()).append("\n");
        }
    }

    // TODO: [FIFO批次管理算法优化] - 优先级: MEDIUM - 参考文档 docs/design/README_OVERVIEW.md
    // 需要优化批次管理算法：
    // 1. 实现严格的FIFO（先进先出）算法
    // 2. 支持批次有效期管理和预警
    // 3. 优化批次分配性能，支持大批量数据处理
    // 4. 提供批次追溯和查询功能
    // 5. 集成质量管理，支持批次质检状态

}
