package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.domain.InboundItem;
import com.iotlaser.spms.wms.domain.InboundItemBatch;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.enums.InboundStatus;
import com.iotlaser.spms.wms.mapper.InboundItemBatchMapper;
import com.iotlaser.spms.wms.mapper.InboundItemMapper;
import com.iotlaser.spms.wms.service.IInboundItemService;
import com.iotlaser.spms.wms.service.IInboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品入库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InboundItemServiceImpl implements IInboundItemService {

    private final InboundItemMapper baseMapper;
    private final InboundItemBatchMapper batchMapper;
    private final ILocationService locationService;
    private final IProductService productService;
    @Lazy
    @Autowired
    private IInboundService inboundService;

    /**
     * 查询产品入库明细
     *
     * @param itemId 主键
     * @return 产品入库明细
     */
    @Override
    public InboundItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询产品入库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库明细分页列表
     */
    @Override
    public TableDataInfo<InboundItemVo> queryPageList(InboundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InboundItem> lqw = buildQueryWrapper(bo);
        Page<InboundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品入库明细列表
     *
     * @param bo 查询条件
     * @return 产品入库明细列表
     */
    @Override
    public List<InboundItemVo> queryList(InboundItemBo bo) {
        LambdaQueryWrapper<InboundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InboundItem> buildQueryWrapper(InboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InboundItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InboundItem::getItemId);
        lqw.eq(bo.getInboundId() != null, InboundItem::getInboundId, bo.getInboundId());
        lqw.eq(bo.getProductId() != null, InboundItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), InboundItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), InboundItem::getProductName, bo.getProductName());
        lqw.eq(bo.getLocationId() != null, InboundItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), InboundItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), InboundItem::getLocationName, bo.getLocationName());

        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InboundItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品入库明细
     *
     * @param bo 产品入库明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InboundItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        InboundItem add = MapstructUtils.convert(bo, InboundItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改产品入库明细
     *
     * @param bo 产品入库明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InboundItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        InboundItem update = MapstructUtils.convert(bo, InboundItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     * <p>
     * 注意：字段非空校验、数据类型校验、格式校验等基础校验已移至Bo类的注解实现
     * 当前方法只负责核心业务逻辑校验：
     * 同一入库单中产品不能重复
     *
     * @param entity 入库明细实体
     */
    private void validEntityBeforeSave(InboundItem entity) {
        // 校验同一入库单中产品不能重复
        if (entity.getInboundId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<InboundItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(InboundItem::getInboundId, entity.getInboundId());
            wrapper.eq(InboundItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(InboundItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一入库单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 批量完成收货数量汇总
     *
     * @param itemId   明细 ID
     * @param batchId  批次 ID
     * @param quantity 数量
     * @return Boolean 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateFinishQuantityBeforeSave(Long itemId, Long batchId, BigDecimal quantity) {
        InboundItem item = baseMapper.queryById(itemId);
        BigDecimal finishQuantity = item.getBatches().stream().filter(batch -> !batch.getBatchId().equals(batchId)).map(InboundItemBatch::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        finishQuantity = finishQuantity.add(quantity);
        if (finishQuantity.compareTo(item.getQuantity()) > 0) {
            throw new ServiceException("产品编号【" + item.getProductCode() + "】产品名称【" + item.getProductName() + "】批次中合计实收数量大于明细中应收货数量");
        }
        InboundItem update = new InboundItem();
        update.setItemId(itemId);
        update.setFinishQuantity(finishQuantity);
        baseMapper.updateById(update);
        return true;
    }

    /**
     * 校验并批量删除产品入库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验入库明细是否可以删除
            List<InboundItem> items = baseMapper.selectByIds(ids);
            for (InboundItem item : items) {
                // 检查主表状态，只有草稿状态的入库明细才能删除
                InboundVo inbound = inboundService.queryById(item.getInboundId());
                if (inbound != null && inbound.getInboundStatus() != InboundStatus.PENDING_RECEIPT) {
                    throw new ServiceException("入库明细所属入库单【" + inbound.getInboundCode() + "】状态为【" + inbound.getInboundStatus().getName() + "】，不允许删除明细");
                }
                log.info("删除入库明细校验通过：产品【{}】", item.getProductName());
            }
        }

        try {
            int batchResult = batchMapper.deleteByItemIds(ids);
            if (batchResult > 0) {
                log.info("批量删除入库明细批次成功，删除数量：{}", batchResult);
            }
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除入库明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除入库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除入库明细失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入产品入库明细表
     *
     * @param items 明细
     * @return 是否插入成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<InboundItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<InboundItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(fillRedundantFields(bo), InboundItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售出库明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售出库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private InboundItemBo fillRedundantFields(InboundItemBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null && StringUtils.isEmpty(bo.getLocationName())) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
        // 填充产品信息
        if (bo.getProductId() != null && StringUtils.isEmpty(bo.getProductName())) {
            ProductVo vo = productService.queryById(bo.getProductId());
            if (vo != null) {
                bo.setProductCode(vo.getProductCode());
                bo.setProductName(vo.getProductName());
                bo.setUnitId(vo.getUnitId());
                bo.setUnitCode(vo.getUnitCode());
                bo.setUnitName(vo.getUnitName());
            }
        }
        return bo;
    }


}
