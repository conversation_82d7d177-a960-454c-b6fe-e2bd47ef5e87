package com.iotlaser.spms.erp.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.common.utils.TaxCalculationUtils;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.mapper.PurchaseInboundItemMapper;
import com.iotlaser.spms.erp.service.IPurchaseInboundItemService;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采购入库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseInboundItemServiceImpl implements IPurchaseInboundItemService {

    private final PurchaseInboundItemMapper baseMapper;
    private final ILocationService locationService;
    private final IProductService productService;
    @Lazy
    @Autowired
    private IPurchaseInboundService purchaseInboundService;

    /**
     * 查询采购入库明细
     *
     * @param itemId 主键
     * @return 采购入库明细
     */
    @Override
    public PurchaseInboundItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询采购入库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库明细分页列表
     */
    @Override
    public TableDataInfo<PurchaseInboundItemVo> queryPageList(PurchaseInboundItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseInboundItem> lqw = buildQueryWrapper(bo);
        Page<PurchaseInboundItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购入库明细列表
     *
     * @param bo 查询条件
     * @return 采购入库明细列表
     */
    @Override
    public List<PurchaseInboundItemVo> queryList(PurchaseInboundItemBo bo) {
        LambdaQueryWrapper<PurchaseInboundItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseInboundItem> buildQueryWrapper(PurchaseInboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseInboundItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseInboundItem::getItemId);
        lqw.eq(bo.getInboundId() != null, PurchaseInboundItem::getInboundId, bo.getInboundId());
        lqw.eq(bo.getProductId() != null, PurchaseInboundItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), PurchaseInboundItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), PurchaseInboundItem::getProductName, bo.getProductName());
        lqw.eq(bo.getQuantity() != null, PurchaseInboundItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, PurchaseInboundItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getPrice() != null, PurchaseInboundItem::getPrice, bo.getPrice());
        lqw.eq(bo.getLocationId() != null, PurchaseInboundItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), PurchaseInboundItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), PurchaseInboundItem::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseInboundItem::getStatus, bo.getStatus());
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), PurchaseInboundItem::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return lqw;
    }

    /**
     * 新增采购入库明细
     *
     * @param bo 采购入库明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PurchaseInboundItemBo bo) {
        //计算填充冗余信息
        fillRedundantFields(bo);
        PurchaseInboundItem add = MapstructUtils.convert(bo, PurchaseInboundItem.class);
        validEntityBeforeSave(add);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改采购入库明细
     *
     * @param bo 采购入库明细
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(PurchaseInboundItemBo bo) {
        //计算填充冗余信息
        fillRedundantFields(bo);
        PurchaseInboundItem update = MapstructUtils.convert(bo, PurchaseInboundItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseInboundItem entity) {
        // 保留核心业务逻辑校验
        if (entity.getFinishQuantity() != null && entity.getQuantity() != null
            && entity.getFinishQuantity().compareTo(entity.getQuantity()) > 0) {
            throw new ServiceException("已完成数量不能大于待完成数量");
        }
    }

    /**
     * 校验并批量删除采购入库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验采购入库明细是否可以删除
            List<PurchaseInboundItem> items = baseMapper.selectByIds(ids);
            for (PurchaseInboundItem item : items) {
                // 检查主表状态，只有草稿状态的入库明细才能删除
                PurchaseInboundVo inbound = purchaseInboundService.queryById(item.getInboundId());
                if (inbound != null && PurchaseInboundStatus.DRAFT != inbound.getInboundStatus()) {
                    throw new ServiceException("入库明细所属采购入库单【" + inbound.getInboundCode() + "】状态为【" + inbound.getInboundStatus() + "】，不允许删除明细");
                }
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除采购入库明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除采购入库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购入库明细失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入采购入库明细表
     *
     * @param items 明细
     * @return 是否插入成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<PurchaseInboundItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<PurchaseInboundItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(fillRedundantFields(bo), PurchaseInboundItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售出库明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售出库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据采购入库单id查询采购入库明细表id集合
     *
     * @param inboundId 采购入库单id
     * @return 采购入库明细表id集合
     */
    @Override
    public List<Long> selectItemIdsByInboundId(Long inboundId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(PurchaseInboundItem.class)
            .select(PurchaseInboundItem::getItemId)
            .eq(PurchaseInboundItem::getInboundId, inboundId)
        ).stream().map(PurchaseInboundItem::getItemId).collect(Collectors.toList());
    }

    /**
     * 入库明细价格计算
     *
     * @param bo 采购入库明细业务对象
     */
    private void calculatePriceFields(PurchaseInboundItemBo bo) {
        if (bo.getPrice() != null && bo.getTaxRate() != null && bo.getFinishQuantity() != null) {
            // 从含税价计算其他价格字段
            TaxCalculationResultBo result = TaxCalculationUtils.calculate(bo.getFinishQuantity(), bo.getTaxRate(), bo.getPrice());
            bo.setPriceExclusiveTax(result.getPriceExclusiveTax());
            bo.setAmount(result.getAmount());
            bo.setAmountExclusiveTax(result.getAmountExclusiveTax());
            bo.setTaxAmount(result.getTaxAmount());

            log.info("采购入库明细价格计算完成 - 数量: {}, 含税价: {}, 税率: {}%, 不含税价: {}, 金额(含税): {}, 金额(不含税): {}, 税额: {}",
                bo.getFinishQuantity(), bo.getPrice(), bo.getTaxRate(), bo.getPriceExclusiveTax(), bo.getAmount(), bo.getAmountExclusiveTax(), bo.getTaxAmount());
        }
    }

    /**
     * 计算填充冗余字段
     */
    private PurchaseInboundItemBo fillRedundantFields(PurchaseInboundItemBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }

        // 填充产品信息
        if (bo.getProductId() != null) {
            ProductVo product = productService.queryById(bo.getProductId());
            if (product != null) {
                bo.setProductCode(product.getProductCode());
                bo.setProductName(product.getProductName());
                bo.setUnitId(product.getUnitId());
                bo.setUnitCode(product.getUnitCode());
                bo.setUnitName(product.getUnitName());
                // 如果没有设置价格，使用产品的采购价格
                if (bo.getPrice() == null && product.getPurchasePrice() != null) {
                    bo.setPrice(product.getPurchasePrice());
                }
                if (bo.getPriceExclusiveTax() == null && product.getPurchasePriceExclusiveTax() != null) {
                    bo.setPriceExclusiveTax(product.getPurchasePriceExclusiveTax());
                }
                if (bo.getTaxRate() == null && product.getPurchaseTaxRate() != null) {
                    bo.setTaxRate(product.getPurchaseTaxRate());
                }
            }
        }
        calculatePriceFields(bo);
        return bo;
    }

}
