package com.iotlaser.spms.pro.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.MeasureUnitVo;
import com.iotlaser.spms.base.service.IMeasureUnitService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.utils.TaxCalculationUtils;
import com.iotlaser.spms.pro.domain.Product;
import com.iotlaser.spms.pro.domain.bo.ProductBo;
import com.iotlaser.spms.pro.domain.vo.ProductCategoryVo;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.mapper.ProductMapper;
import com.iotlaser.spms.pro.service.IBomService;
import com.iotlaser.spms.pro.service.IProductCategoryService;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_PRODUCT_CODE;

/**
 * 产品信息Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductServiceImpl implements IProductService {

    private final ProductMapper baseMapper;
    private final IBomService bomService;
    private final Gen gen;
    private final IMeasureUnitService baseMeasureUnitService;
    private final IProductCategoryService productCategoryService;

    /**
     * 查询产品信息
     *
     * @param productId 主键
     * @return 产品信息
     */
    @Override
    public ProductVo queryById(Long productId) {
        return baseMapper.selectVoById(productId);
    }

    /**
     * 分页查询产品信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品信息分页列表
     */
    @Override
    public TableDataInfo<ProductVo> queryPageList(ProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Product> lqw = buildQueryWrapper(bo);
        Page<ProductVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品信息列表
     *
     * @param bo 查询条件
     * @return 产品信息列表
     */
    @Override
    public List<ProductVo> queryList(ProductBo bo) {
        LambdaQueryWrapper<Product> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Product> buildQueryWrapper(ProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Product> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Product::getProductId);
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), Product::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), Product::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Product::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSpecs()), Product::getProductSpecs, bo.getProductSpecs());
        lqw.eq(StringUtils.isNotBlank(bo.getProductType()), Product::getProductType, bo.getProductType());
        lqw.eq(bo.getUnitId() != null, Product::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), Product::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), Product::getUnitName, bo.getUnitName());
        lqw.eq(bo.getCategoryId() != null, Product::getCategoryId, bo.getCategoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryCode()), Product::getCategoryCode, bo.getCategoryCode());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), Product::getCategoryName, bo.getCategoryName());
        lqw.eq(StringUtils.isNotBlank(bo.getBatchFlag()), Product::getBatchFlag, bo.getBatchFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getSafeStockFlag()), Product::getSafeStockFlag, bo.getSafeStockFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getHighValueFlag()), Product::getHighValueFlag, bo.getHighValueFlag());
        lqw.eq(bo.getMinStock() != null, Product::getMinStock, bo.getMinStock());
        lqw.eq(bo.getMaxStock() != null, Product::getMaxStock, bo.getMaxStock());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Product::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品信息
     *
     * @param bo 产品信息
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(ProductBo bo) {
        if (StringUtils.isEmpty(bo.getProductCode())) {
            bo.setProductCode(gen.code(PRO_PRODUCT_CODE));
        }
        Product add = MapstructUtils.convert(bo, Product.class);
        validEntityBeforeSave(add);
        redundancyEntityBeforeSave(add);
        calculatePrices(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setProductId(add.getProductId());
            log.info("新增产品信息成功，产品编码：{}", add.getProductCode());
        }
        return flag;
    }

    /**
     * 修改产品信息
     *
     * @param bo 产品信息
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(ProductBo bo) {
        Product update = MapstructUtils.convert(bo, Product.class);
        validEntityBeforeSave(update);
        redundancyEntityBeforeSave(update);
        calculatePrices(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            log.info("修改产品信息成功，产品编码：{}", update.getProductCode());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Product entity) {
        // 校验产品编码唯一性
        if (StringUtils.isNotBlank(entity.getProductCode())) {
            LambdaQueryWrapper<Product> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Product::getProductCode, entity.getProductCode());
            if (entity.getProductId() != null) {
                wrapper.ne(Product::getProductId, entity.getProductId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("产品编码已存在：" + entity.getProductCode());
            }
        }
    }

    /**
     * 保存前的数据填充
     */
    private void redundancyEntityBeforeSave(Product entity) {
        MeasureUnitVo unit = baseMeasureUnitService.queryById(entity.getUnitId());
        if (unit != null) {
            entity.setUnitCode(unit.getUnitCode());
            entity.setUnitName(unit.getUnitName());
        }
        ProductCategoryVo category = productCategoryService.queryById(entity.getCategoryId());
        if (category != null) {
            entity.setCategoryCode(category.getCategoryCode());
            entity.setCategoryName(category.getCategoryName());
        }
    }

    /**
     * 校验并批量删除产品信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验产品是否被其他业务数据引用
            List<Product> products = baseMapper.selectByIds(ids);
            for (Product product : products) {
                // 检查是否被BOM引用
                checkProductBomRelation(product.getProductId(), product.getProductName());

                // 检查是否有关联的库存记录
                // TODO: 添加inventoryService依赖注入
                // if (inventoryService.existsByProductId(product.getProductId())) {
                //     throw new ServiceException("产品【" + product.getProductName() + "】存在库存记录，不允许删除");
                // }

                // 检查是否有关联的采购订单明细
                // TODO: 添加purchaseOrderItemService依赖注入
                // if (purchaseOrderItemService.existsByProductId(product.getProductId())) {
                //     throw new ServiceException("产品【" + product.getProductName() + "】存在采购订单明细，不允许删除");
                // }

                // 检查是否有关联的销售订单明细
                // TODO: 添加saleOrderItemService依赖注入
                // if (saleOrderItemService.existsByProductId(product.getProductId())) {
                //     throw new ServiceException("产品【" + product.getProductName() + "】存在销售订单明细，不允许删除");
                // }

                log.info("删除产品校验通过，产品编码：{}", product.getProductCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除产品信息成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除产品信息失败：{}", e.getMessage(), e);
            throw new ServiceException("删除产品信息失败：" + e.getMessage());
        }
    }

    /**
     * 检查产品BOM关联关系
     * ✅ 修正：使用Service接口而非直接调用Mapper
     *
     * @param productId   产品ID
     * @param productName 产品名称
     */
    private void checkProductBomRelation(Long productId, String productName) {
        // TODO: 集成BOM模块，检查产品的BOM使用情况
        // 需要实现：
        // 检查产品是否作为BOM主产品被使用
        // 检查产品是否作为BOM组件被使用
        // 确保数据一致性和引用完整性

        // ✅ 正确的实现方式：通过Service接口调用
        // 实现示例：
        // BomBo bomQuery = new BomBo();
        // bomQuery.setProductId(productId);
        // List<BomVo> boms = bomService.queryList(bomQuery);
        // if (!boms.isEmpty()) {
        //     throw new ServiceException("产品【" + productName + "】已被BOM引用，不允许删除");
        // }

        // BomItemBo bomItemQuery = new BomItemBo();
        // bomItemQuery.setProductId(productId);
        // List<BomItemVo> bomItems = bomItemService.queryList(bomItemQuery);
        // if (!bomItems.isEmpty()) {
        //     throw new ServiceException("产品【" + productName + "】已被BOM明细引用，不允许删除");
        // }

        log.debug("检查产品【{}】的BOM关联关系", productName);
    }

    /**
     * 计算价格（含税价与不含税价的自动计算）
     *
     * @param product 产品实体
     */
    private void calculatePrices(Product product) {
        // 计算采购价格
        calculatePurchasePrices(product);

        // 计算销售价格
        calculateSalePrices(product);
    }

    /**
     * 计算采购价格
     * <p>
     * 重构后的实现，使用统一的 TaxCalculationUtils 进行计算
     *
     * @param product 产品实体
     */
    private void calculatePurchasePrices(Product product) {
        try {
            if (product.getPurchasePrice() != null && product.getPurchaseTaxRate() != null) {
                // 如果有含税价和税率，计算不含税价
                BigDecimal exclusiveTaxPrice = TaxCalculationUtils.calculateExclusivePrice(
                    product.getPurchasePrice(), product.getPurchaseTaxRate());
                product.setPurchasePriceExclusiveTax(exclusiveTaxPrice);

                log.debug("计算采购不含税价格完成 - 含税价: {}, 税率: {}%, 不含税价: {}",
                    product.getPurchasePrice(), product.getPurchaseTaxRate(), exclusiveTaxPrice);

            } else if (product.getPurchasePriceExclusiveTax() != null && product.getPurchaseTaxRate() != null) {
                // 如果有不含税价和税率，计算含税价
                BigDecimal inclusiveTaxPrice = TaxCalculationUtils.calculateInclusivePrice(
                    product.getPurchasePriceExclusiveTax(), product.getPurchaseTaxRate());
                product.setPurchasePrice(inclusiveTaxPrice);

                log.debug("计算采购含税价格完成 - 不含税价: {}, 税率: {}%, 含税价: {}",
                    product.getPurchasePriceExclusiveTax(), product.getPurchaseTaxRate(), inclusiveTaxPrice);
            }
        } catch (Exception e) {
            log.error("计算采购价格失败 - 产品编码: {}, 错误: {}", product.getProductCode(), e.getMessage(), e);
            throw new ServiceException("计算采购价格失败: " + e.getMessage());
        }
    }

    /**
     * 计算销售价格
     * <p>
     * 重构后的实现，使用统一的 TaxCalculationUtils 进行计算
     *
     * @param product 产品实体
     */
    private void calculateSalePrices(Product product) {
        try {
            if (product.getSalePrice() != null && product.getSaleTaxRate() != null) {
                // 如果有含税价和税率，计算不含税价
                BigDecimal exclusiveTaxPrice = TaxCalculationUtils.calculateExclusivePrice(
                    product.getSalePrice(), product.getSaleTaxRate());
                product.setSalePriceExclusiveTax(exclusiveTaxPrice);

                log.debug("计算销售不含税价格完成 - 含税价: {}, 税率: {}%, 不含税价: {}",
                    product.getSalePrice(), product.getSaleTaxRate(), exclusiveTaxPrice);

            } else if (product.getSalePriceExclusiveTax() != null && product.getSaleTaxRate() != null) {
                // 如果有不含税价和税率，计算含税价
                BigDecimal inclusiveTaxPrice = TaxCalculationUtils.calculateInclusivePrice(
                    product.getSalePriceExclusiveTax(), product.getSaleTaxRate());
                product.setSalePrice(inclusiveTaxPrice);

                log.debug("计算销售含税价格完成 - 不含税价: {}, 税率: {}%, 含税价: {}",
                    product.getSalePriceExclusiveTax(), product.getSaleTaxRate(), inclusiveTaxPrice);
            }
        } catch (Exception e) {
            log.error("计算销售价格失败 - 产品编码: {}, 错误: {}", product.getProductCode(), e.getMessage(), e);
            throw new ServiceException("计算销售价格失败: " + e.getMessage());
        }
    }

    /**
     * 计算含税价格
     * <p>
     * 重构后的实现，使用统一的 TaxCalculationUtils 进行计算
     *
     * @param exclusiveTaxPrice 不含税价格
     * @param taxRate           税率（百分比格式，如 13 表示 13%）
     * @return 含税价格
     * @throws ServiceException 当计算失败时
     */
    @Override
    public BigDecimal calculateInclusiveTaxPrice(BigDecimal exclusiveTaxPrice, BigDecimal taxRate) {
        log.debug("计算含税价格 - 不含税价格: {}, 税率: {}%", exclusiveTaxPrice, taxRate);

        if (exclusiveTaxPrice == null || taxRate == null) {
            log.warn("计算含税价格时参数为空，返回null - 不含税价格: {}, 税率: {}", exclusiveTaxPrice, taxRate);
            return null;
        }

        try {
            // 使用统一工具类进行计算
            BigDecimal result = TaxCalculationUtils.calculateInclusivePrice(exclusiveTaxPrice, taxRate);

            log.debug("含税价格计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("含税价格计算失败 - 不含税价格: {}, 税率: {}%, 错误: {}", exclusiveTaxPrice, taxRate, e.getMessage());
            throw new ServiceException("含税价格计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算不含税价格
     * <p>
     * 重构后的实现，使用统一的 TaxCalculationUtils 进行计算
     *
     * @param inclusiveTaxPrice 含税价格
     * @param taxRate           税率（百分比格式，如 13 表示 13%）
     * @return 不含税价格
     * @throws ServiceException 当计算失败时
     */
    @Override
    public BigDecimal calculateExclusiveTaxPrice(BigDecimal inclusiveTaxPrice, BigDecimal taxRate) {
        log.debug("计算不含税价格 - 含税价格: {}, 税率: {}%", inclusiveTaxPrice, taxRate);

        if (inclusiveTaxPrice == null || taxRate == null) {
            log.warn("计算不含税价格时参数为空，返回null - 含税价格: {}, 税率: {}", inclusiveTaxPrice, taxRate);
            return null;
        }

        try {
            // 使用统一工具类进行计算
            BigDecimal result = TaxCalculationUtils.calculateExclusivePrice(inclusiveTaxPrice, taxRate);

            log.debug("不含税价格计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("不含税价格计算失败 - 含税价格: {}, 税率: {}%, 错误: {}", inclusiveTaxPrice, taxRate, e.getMessage());
            throw new ServiceException("不含税价格计算失败: " + e.getMessage());
        }
    }
}
