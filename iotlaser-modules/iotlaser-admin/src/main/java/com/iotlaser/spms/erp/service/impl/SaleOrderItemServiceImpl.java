package com.iotlaser.spms.erp.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.common.utils.TaxCalculationUtils;
import com.iotlaser.spms.erp.domain.SaleOrderItem;
import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import com.iotlaser.spms.erp.mapper.SaleOrderItemMapper;
import com.iotlaser.spms.erp.service.ISaleOrderItemService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售订单明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOrderItemServiceImpl implements ISaleOrderItemService {

    private final SaleOrderItemMapper baseMapper;
    private final IProductService productService;

    /**
     * 查询销售订单明细
     *
     * @param itemId 主键
     * @return 销售订单明细
     */
    @Override
    public SaleOrderItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询销售订单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单明细分页列表
     */
    @Override
    public TableDataInfo<SaleOrderItemVo> queryPageList(SaleOrderItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOrderItem> lqw = buildQueryWrapper(bo);
        Page<SaleOrderItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售订单明细列表
     *
     * @param bo 查询条件
     * @return 销售订单明细列表
     */
    @Override
    public List<SaleOrderItemVo> queryList(SaleOrderItemBo bo) {
        LambdaQueryWrapper<SaleOrderItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleOrderItem> buildQueryWrapper(SaleOrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOrderItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOrderItem::getItemId);
        lqw.eq(bo.getOrderId() != null, SaleOrderItem::getOrderId, bo.getOrderId());
        lqw.eq(bo.getProductId() != null, SaleOrderItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), SaleOrderItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), SaleOrderItem::getProductName, bo.getProductName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SaleOrderItem::getStatus, bo.getStatus());
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), SaleOrderItem::getProductId, StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return lqw;
    }

    /**
     * 新增销售订单明细
     *
     * @param bo 销售订单明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SaleOrderItemBo bo) {
        // 填充冗余信息
        fillRedundantFields(bo);

        SaleOrderItem add = MapstructUtils.convert(bo, SaleOrderItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改销售订单明细
     *
     * @param bo 销售订单明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SaleOrderItemBo bo) {
        // 填充冗余信息
        fillRedundantFields(bo);

        SaleOrderItem update = MapstructUtils.convert(bo, SaleOrderItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOrderItem entity) {
        // 校验同一订单中产品不能重复
        if (entity.getOrderId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<SaleOrderItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOrderItem::getOrderId, entity.getOrderId());
            wrapper.eq(SaleOrderItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(SaleOrderItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一销售订单中不能重复添加相同产品");
            }
        }

        if (entity.getItemId() != null) {
            if (entity.getInvoicedQuantity().compareTo(entity.getQuantity()) > 0) {
                throw new ServiceException("已开票数量不能大于销售数量");
            }
            if (entity.getShippedQuantity().compareTo(entity.getQuantity()) > 0) {
                throw new ServiceException("已发货数量不能大于销售数量");
            }
            if (entity.getReturnedQuantity().compareTo(entity.getQuantity()) > 0) {
                throw new ServiceException("退货数量不能大于销售数量");
            }
        }
    }

    /**
     * 校验并批量删除销售订单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<SaleOrderItem> items = baseMapper.selectByIds(ids);
            for (SaleOrderItem item : items) {
                // 检查关联的销售订单状态
                // 检查关联的销售订单状态
                log.info("删除销售订单明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据订单ID查询明细列表
     *
     * @param orderId 订单ID
     * @return 明细列表
     */
    @Override
    public List<SaleOrderItemVo> queryByOrderId(Long orderId) {
        return baseMapper.queryByOrderId(orderId);
    }

    /**
     * 批量插入或更新销售明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<SaleOrderItemBo> items) {
        try {
            List<SaleOrderItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(fillRedundantFields(bo), SaleOrderItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 填充产品价格信息
     *
     * @param bo 采购明细
     * @return 采购明细
     */
    private SaleOrderItemBo fillRedundantFields(SaleOrderItemBo bo) {
        // 填充产品信息
        if (bo.getProductId() != null) {
            ProductVo product = productService.queryById(bo.getProductId());
            if (product != null) {
                bo.setProductCode(product.getProductCode());
                bo.setProductName(product.getProductName());
                bo.setUnitId(product.getUnitId());
                bo.setUnitCode(product.getUnitCode());
                bo.setUnitName(product.getUnitName());
                // 如果没有设置价格，使用产品的销售价格
                if (bo.getPrice() == null && product.getSalePrice() != null) {
                    bo.setPrice(product.getSalePrice());
                }
                if (bo.getPriceExclusiveTax() == null && product.getSalePriceExclusiveTax() != null) {
                    bo.setPriceExclusiveTax(product.getSalePriceExclusiveTax());
                }
                if (bo.getTaxRate() == null && product.getSaleTaxRate() != null) {
                    bo.setTaxRate(product.getSaleTaxRate());
                }
            }
        }
        // 填充价格信息
        if (bo.getPrice() != null && bo.getTaxRate() != null && bo.getQuantity() != null) {
            // 从含税价计算其他价格字段
            TaxCalculationResultBo result = TaxCalculationUtils.calculate(bo.getQuantity(), bo.getTaxRate(), bo.getPrice());
            bo.setPriceExclusiveTax(result.getPriceExclusiveTax());
            bo.setAmount(result.getAmount());
            bo.setAmountExclusiveTax(result.getAmountExclusiveTax());
            bo.setTaxAmount(result.getTaxAmount());

            log.info("采购入库明细价格计算完成 - 数量: {}, 含税价: {}, 税率: {}%, 不含税价: {}, 金额(含税): {}, 金额(不含税): {}, 税额: {}",
                bo.getQuantity(), bo.getPrice(), bo.getTaxRate(), bo.getPriceExclusiveTax(), bo.getAmount(), bo.getAmountExclusiveTax(), bo.getTaxAmount());
        }
        return bo;
    }


    /**
     * 查询销售订单明细表及其关联信息
     *
     * @param itemId 主键
     * @return 销售订单明细表
     */
    @Override
    public SaleOrderItemVo queryByIdWith(Long itemId) {
        return MapstructUtils.convert(baseMapper.queryByIdWith(itemId), SaleOrderItemVo.class);
    }

    /**
     * 分页查询销售订单明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单明细表分页列表
     */
    @Override
    public TableDataInfo<SaleOrderItemVo> queryPageListWith(SaleOrderItemBo bo, PageQuery pageQuery) {
        QueryWrapper<SaleOrderItem> queryWrapper = buildQueryWrapperWith(bo);
        List<SaleOrderItemVo> result = MapstructUtils.convert(baseMapper.queryPageListWith(pageQuery.build(), queryWrapper), SaleOrderItemVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<SaleOrderItem> buildQueryWrapperWith(SaleOrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<SaleOrderItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getOrderId() != null, "item.order_id", bo.getOrderId());
        wrapper.eq(bo.getInventoryId() != null, "item.inventory_id", bo.getInventoryId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getShippedQuantity() != null, "item.shipped_quantity", bo.getShippedQuantity());
        wrapper.eq(bo.getReturnedQuantity() != null, "item.returned_quantity", bo.getReturnedQuantity());
        wrapper.eq(bo.getPrice() != null, "item.price", bo.getPrice());
        wrapper.eq(bo.getPriceExclusiveTax() != null, "item.price_exclusive_tax", bo.getPriceExclusiveTax());
        wrapper.eq(bo.getAmount() != null, "item.amount", bo.getAmount());
        wrapper.eq(bo.getAmountExclusiveTax() != null, "item.amount_exclusive_tax", bo.getAmountExclusiveTax());
        wrapper.eq(bo.getTaxRate() != null, "item.tax_rate", bo.getTaxRate());
        wrapper.eq(bo.getTaxAmount() != null, "item.tax_amount", bo.getTaxAmount());
        wrapper.eq(bo.getInvoicedQuantity() != null, "item.invoiced_quantity", bo.getInvoicedQuantity());
        wrapper.eq(bo.getInvoicedAmount() != null, "item.invoiced_amount", bo.getInvoicedAmount());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        wrapper.notIn(StringUtils.isNotBlank(bo.getExcludeProductIds()), "item.product_id", StringUtils.splitTo(bo.getExcludeProductIds(), Convert::toLong));
        return wrapper;
    }


}
